<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碰撞检测调试 - 瞬光捕手</title>
    <link rel="stylesheet" href="styles/main.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .debug-info {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            color: white;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        
        .control-btn {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
            color: white;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .control-btn:hover {
            background: rgba(76, 175, 80, 0.3);
        }
        
        .control-btn.danger {
            background: rgba(244, 67, 54, 0.2);
            border-color: #F44336;
        }
        
        .control-btn.danger:hover {
            background: rgba(244, 67, 54, 0.3);
        }
        
        #game-canvas {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            display: block;
            margin: 20px auto;
            cursor: crosshair;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            color: white;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1 style="color: white; text-align: center; margin-bottom: 20px;">🎯 碰撞检测调试工具</h1>
        
        <div class="controls">
            <button class="control-btn" onclick="startDebugGame()">开始调试游戏</button>
            <button class="control-btn" onclick="spawnTestSpark()">生成测试光点</button>
            <button class="control-btn" onclick="toggleDebugMode()">切换调试模式</button>
            <button class="control-btn danger" onclick="clearDebugLog()">清空日志</button>
            <button class="control-btn danger" onclick="resetGame()">重置游戏</button>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value" id="hit-count">0</div>
                <div class="stat-label">成功击中</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="miss-count">0</div>
                <div class="stat-label">错过点击</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="accuracy">0%</div>
                <div class="stat-label">命中率</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="spark-count">0</div>
                <div class="stat-label">当前光点数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="lives-count">3</div>
                <div class="stat-label">剩余生命</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="click-count">0</div>
                <div class="stat-label">总点击次数</div>
            </div>
        </div>
        
        <canvas id="game-canvas" width="800" height="600"></canvas>
        
        <div class="debug-info" id="debug-log">
            <div>🔍 调试日志将在这里显示...</div>
            <div>💡 点击画布上的光点来测试碰撞检测</div>
            <div>📊 观察命中率和调试信息</div>
        </div>
    </div>

    <!-- 游戏脚本 -->
    <script src="js/core/game-engine.js"></script>
    <script src="js/utils/touch-helper.js"></script>
    
    <script>
        let debugEngine = null;
        let debugMode = true;
        let hitCount = 0;
        let missCount = 0;
        let clickCount = 0;
        let originalConsoleLog = console.log;
        
        // 重写console.log来捕获调试信息
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            if (debugMode) {
                const logElement = document.getElementById('debug-log');
                const message = args.join(' ');
                const div = document.createElement('div');
                div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
                logElement.appendChild(div);
                logElement.scrollTop = logElement.scrollHeight;
            }
        };
        
        function startDebugGame() {
            const canvas = document.getElementById('game-canvas');
            debugEngine = new GameEngine();
            debugEngine.init(canvas);
            debugEngine.startGame();
            
            // 添加点击监听器
            canvas.addEventListener('click', (e) => {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                clickCount++;
                console.log(`🖱️ 点击位置: (${x.toFixed(2)}, ${y.toFixed(2)}) - 第${clickCount}次点击`);
                debugEngine.handleClick(x, y);
                updateStats();
            });
            
            console.log('🎮 调试游戏已启动');
        }
        
        function spawnTestSpark() {
            if (!debugEngine) {
                console.log('❌ 请先启动调试游戏');
                return;
            }
            
            debugEngine.createSpark();
            console.log('✨ 已生成测试光点');
            updateStats();
        }
        
        function toggleDebugMode() {
            debugMode = !debugMode;
            console.log(`🔧 调试模式: ${debugMode ? '开启' : '关闭'}`);
        }
        
        function clearDebugLog() {
            document.getElementById('debug-log').innerHTML = '<div>🔍 调试日志已清空</div>';
        }
        
        function resetGame() {
            if (debugEngine) {
                debugEngine.gameState = 'menu';
                debugEngine.sparks = [];
                debugEngine.particles = [];
                debugEngine.score = 0;
                debugEngine.lives = 3;
                debugEngine.combo = 0;
            }
            hitCount = 0;
            missCount = 0;
            clickCount = 0;
            updateStats();
            console.log('🔄 游戏已重置');
        }
        
        function updateStats() {
            if (debugEngine) {
                document.getElementById('spark-count').textContent = debugEngine.sparks.length;
                document.getElementById('lives-count').textContent = debugEngine.lives;
            }

            // 统计命中和错过（通过监听console.log）
            const totalClicks = hitCount + missCount;
            const accuracy = totalClicks > 0 ? ((hitCount / totalClicks) * 100).toFixed(1) : 0;

            document.getElementById('hit-count').textContent = hitCount;
            document.getElementById('miss-count').textContent = missCount;
            document.getElementById('accuracy').textContent = accuracy + '%';
            document.getElementById('click-count').textContent = clickCount;
        }
        
        // 监听游戏引擎的击中和错过事件
        const originalHitSpark = GameEngine.prototype.hitSpark;
        const originalMissClick = GameEngine.prototype.missClick;
        
        GameEngine.prototype.hitSpark = function(spark, index) {
            hitCount++;
            return originalHitSpark.call(this, spark, index);
        };
        
        GameEngine.prototype.missClick = function() {
            missCount++;
            return originalMissClick.call(this);
        };
        
        // 页面加载完成后自动启动
        window.addEventListener('load', () => {
            console.log('🚀 碰撞检测调试工具已加载');
            console.log('💡 点击"开始调试游戏"按钮开始测试');
        });
    </script>
</body>
</html>
