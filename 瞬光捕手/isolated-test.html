<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隔离测试 - 瞬光捕手</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔬 隔离测试 - 模块加载问题诊断</h1>
    
    <div id="test-results"></div>

    <script>
        const results = document.getElementById('test-results');
        
        function addResult(title, content, type = 'test-section') {
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = `<h3>${title}</h3><div>${content}</div>`;
            results.appendChild(div);
        }
        
        // 全局错误捕获
        const errors = [];
        window.addEventListener('error', (event) => {
            errors.push({
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });
        
        // 测试1: 检查基础环境
        addResult('测试1: 基础环境检查', `
            <p>✅ window 对象存在: ${typeof window !== 'undefined'}</p>
            <p>✅ document 对象存在: ${typeof document !== 'undefined'}</p>
            <p>✅ console 对象存在: ${typeof console !== 'undefined'}</p>
        `, 'success');
        
        // 测试2: 尝试创建简单的类
        try {
            class TestClass {
                constructor() {
                    this.test = true;
                }
            }
            const testInstance = new TestClass();
            window.testInstance = testInstance;
            
            addResult('测试2: 类创建测试', `
                <p>✅ 类定义成功</p>
                <p>✅ 实例创建成功</p>
                <p>✅ 全局变量赋值成功: ${window.testInstance ? '是' : '否'}</p>
            `, 'success');
        } catch (error) {
            addResult('测试2: 类创建测试', `
                <p>❌ 错误: ${error.message}</p>
            `, 'error');
        }
        
        // 测试3: 逐步加载依赖模块
        const modules = [
            'js/utils/storage.js',
            'js/utils/i18n.js', 
            'js/core/game-engine.js',
            'js/core/player-manager.js',
            'js/core/level-manager.js',
            'js/core/leaderboard-manager.js',
            'js/core/level-editor.js'
        ];
        
        let loadedModules = 0;
        const moduleStatus = {};
        
        function loadNextModule() {
            if (loadedModules >= modules.length) {
                // 所有依赖模块加载完成，现在测试问题模块
                testProblemModules();
                return;
            }
            
            const modulePath = modules[loadedModules];
            const script = document.createElement('script');
            script.src = modulePath;
            
            script.onload = () => {
                moduleStatus[modulePath] = 'loaded';
                loadedModules++;
                loadNextModule();
            };
            
            script.onerror = () => {
                moduleStatus[modulePath] = 'failed';
                loadedModules++;
                loadNextModule();
            };
            
            document.head.appendChild(script);
        }
        
        function testProblemModules() {
            // 显示依赖模块加载状态
            let dependencyStatus = '<h4>依赖模块状态:</h4><ul>';
            Object.keys(moduleStatus).forEach(module => {
                const status = moduleStatus[module];
                const icon = status === 'loaded' ? '✅' : '❌';
                dependencyStatus += `<li>${icon} ${module}: ${status}</li>`;
            });
            dependencyStatus += '</ul>';
            
            // 检查全局变量
            const globals = ['storageService', 'i18nService', 'gameEngine', 'playerManager', 'levelManager', 'leaderboardManager', 'levelEditor'];
            let globalStatus = '<h4>全局变量状态:</h4><ul>';
            globals.forEach(name => {
                const exists = window[name] ? '✅' : '❌';
                globalStatus += `<li>${exists} window.${name}: ${window[name] ? '已定义' : '未定义'}</li>`;
            });
            globalStatus += '</ul>';
            
            addResult('测试3: 依赖模块加载', dependencyStatus + globalStatus, 'success');
            
            // 现在测试问题模块
            testScreenManager();
        }
        
        function testScreenManager() {
            addResult('测试4: ScreenManager 加载测试', '<p>🔄 开始测试...</p>', 'warning');
            
            const script = document.createElement('script');
            script.src = 'js/ui/screen-manager.js';
            
            script.onload = () => {
                setTimeout(() => {
                    let result = '<h4>ScreenManager 加载结果:</h4>';
                    
                    if (window.screenManager) {
                        result += '<p>✅ screenManager 全局变量已创建</p>';
                        result += `<p>📊 类型: ${typeof window.screenManager}</p>`;
                        result += `<p>📊 构造函数: ${window.screenManager.constructor.name}</p>`;
                        
                        // 检查属性
                        const props = ['currentScreen', 'screens', 'overlays', 'initialized'];
                        props.forEach(prop => {
                            result += `<p>📋 ${prop}: ${window.screenManager[prop]}</p>`;
                        });
                        
                        // 检查方法
                        const methods = ['init', 'registerScreens', 'bindEvents'];
                        methods.forEach(method => {
                            const exists = typeof window.screenManager[method] === 'function';
                            const icon = exists ? '✅' : '❌';
                            result += `<p>${icon} ${method}(): ${exists ? '存在' : '不存在'}</p>`;
                        });
                        
                        updateResult('测试4: ScreenManager 加载测试', result, 'success');
                        
                        // 测试 InputHandler
                        testInputHandler();
                    } else {
                        result += '<p>❌ screenManager 全局变量未创建</p>';
                        updateResult('测试4: ScreenManager 加载测试', result, 'error');
                    }
                }, 100);
            };
            
            script.onerror = (error) => {
                updateResult('测试4: ScreenManager 加载测试', `<p>❌ 脚本加载失败: ${error}</p>`, 'error');
            };
            
            document.head.appendChild(script);
        }
        
        function testInputHandler() {
            addResult('测试5: InputHandler 加载测试', '<p>🔄 开始测试...</p>', 'warning');
            
            const script = document.createElement('script');
            script.src = 'js/ui/input-handler.js';
            
            script.onload = () => {
                setTimeout(() => {
                    let result = '<h4>InputHandler 加载结果:</h4>';
                    
                    if (window.inputHandler) {
                        result += '<p>✅ inputHandler 全局变量已创建</p>';
                        result += `<p>📊 类型: ${typeof window.inputHandler}</p>`;
                        result += `<p>📊 构造函数: ${window.inputHandler.constructor.name}</p>`;
                        
                        updateResult('测试5: InputHandler 加载测试', result, 'success');
                    } else {
                        result += '<p>❌ inputHandler 全局变量未创建</p>';
                        updateResult('测试5: InputHandler 加载测试', result, 'error');
                    }
                    
                    // 显示所有错误
                    showErrors();
                }, 100);
            };
            
            script.onerror = (error) => {
                updateResult('测试5: InputHandler 加载测试', `<p>❌ 脚本加载失败: ${error}</p>`, 'error');
                showErrors();
            };
            
            document.head.appendChild(script);
        }
        
        function updateResult(title, content, type) {
            const sections = document.querySelectorAll('.test-section');
            sections.forEach(section => {
                if (section.querySelector('h3').textContent === title) {
                    section.className = `test-section ${type}`;
                    section.querySelector('div').innerHTML = content;
                }
            });
        }
        
        function showErrors() {
            if (errors.length > 0) {
                let errorContent = '<h4>捕获的错误:</h4><ul>';
                errors.forEach(error => {
                    errorContent += `<li>❌ ${error.message} (${error.filename}:${error.lineno})</li>`;
                });
                errorContent += '</ul>';
                
                addResult('错误报告', errorContent, 'error');
            } else {
                addResult('错误报告', '<p>✅ 没有捕获到错误</p>', 'success');
            }
        }
        
        // 开始测试
        setTimeout(() => {
            loadNextModule();
        }, 1000);
    </script>
</body>
</html>
