<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模块调试 - 瞬光捕手</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background: #1a1a1a;
            color: #00ff00;
        }
        .log {
            background: #000;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #333;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        .error { color: #ff4444; }
        .success { color: #44ff44; }
        .warning { color: #ffaa44; }
        .info { color: #4488ff; }
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #555;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        button:hover { background: #555; }
    </style>
</head>
<body>
    <h1>🔧 模块调试工具</h1>
    
    <div>
        <button onclick="loadScreenManager()">加载 ScreenManager</button>
        <button onclick="loadInputHandler()">加载 InputHandler</button>
        <button onclick="checkModules()">检查所有模块</button>
        <button onclick="clearLog()">清除日志</button>
    </div>
    
    <div id="log" class="log"></div>

    <!-- 先加载依赖模块 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/core/game-engine.js"></script>
    <script src="js/core/player-manager.js"></script>
    <script src="js/core/level-manager.js"></script>
    <script src="js/core/leaderboard-manager.js"></script>
    <script src="js/core/level-editor.js"></script>

    <script>
        const logDiv = document.getElementById('log');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.className = type;
            div.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(div);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            logDiv.innerHTML = '';
        }
        
        // 全局错误捕获
        window.addEventListener('error', (event) => {
            log(`❌ 全局错误: ${event.message} (${event.filename}:${event.lineno})`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            log(`❌ Promise拒绝: ${event.reason}`, 'error');
        });
        
        function loadScreenManager() {
            log('🔄 开始加载 ScreenManager...', 'info');
            
            // 检查依赖
            const dependencies = ['storageService', 'i18nService', 'gameEngine', 'levelEditor'];
            let missingDeps = [];
            
            dependencies.forEach(dep => {
                if (!window[dep]) {
                    missingDeps.push(dep);
                }
            });
            
            if (missingDeps.length > 0) {
                log(`⚠️ 缺少依赖: ${missingDeps.join(', ')}`, 'warning');
            } else {
                log('✅ 所有依赖已加载', 'success');
            }
            
            // 动态加载 screen-manager.js
            const script = document.createElement('script');
            script.src = 'js/ui/screen-manager.js';
            
            script.onload = () => {
                log('📦 screen-manager.js 脚本加载完成', 'success');
                
                setTimeout(() => {
                    if (window.screenManager) {
                        log('✅ screenManager 全局变量已创建', 'success');
                        log(`📊 screenManager 类型: ${typeof window.screenManager}`, 'info');
                        
                        // 检查方法
                        if (typeof window.screenManager.init === 'function') {
                            log('✅ screenManager.init 方法存在', 'success');
                        } else {
                            log('❌ screenManager.init 方法不存在', 'error');
                        }
                    } else {
                        log('❌ screenManager 全局变量未创建', 'error');
                    }
                }, 100);
            };
            
            script.onerror = (error) => {
                log(`❌ screen-manager.js 脚本加载失败: ${error}`, 'error');
            };
            
            document.head.appendChild(script);
        }
        
        function loadInputHandler() {
            log('🔄 开始加载 InputHandler...', 'info');
            
            // 动态加载 input-handler.js
            const script = document.createElement('script');
            script.src = 'js/ui/input-handler.js';
            
            script.onload = () => {
                log('📦 input-handler.js 脚本加载完成', 'success');
                
                setTimeout(() => {
                    if (window.inputHandler) {
                        log('✅ inputHandler 全局变量已创建', 'success');
                        log(`📊 inputHandler 类型: ${typeof window.inputHandler}`, 'info');
                        
                        // 检查方法
                        if (typeof window.inputHandler.init === 'function') {
                            log('✅ inputHandler.init 方法存在', 'success');
                        } else {
                            log('❌ inputHandler.init 方法不存在', 'error');
                        }
                    } else {
                        log('❌ inputHandler 全局变量未创建', 'error');
                    }
                }, 100);
            };
            
            script.onerror = (error) => {
                log(`❌ input-handler.js 脚本加载失败: ${error}`, 'error');
            };
            
            document.head.appendChild(script);
        }
        
        function checkModules() {
            log('🔍 检查所有模块状态...', 'info');
            
            const modules = [
                'storageService', 'i18nService', 'gameEngine', 'playerManager',
                'levelManager', 'leaderboardManager', 'levelEditor', 'screenManager', 'inputHandler'
            ];
            
            modules.forEach(moduleName => {
                if (window[moduleName]) {
                    log(`✅ ${moduleName} 已加载`, 'success');
                } else {
                    log(`❌ ${moduleName} 未加载`, 'error');
                }
            });
        }
        
        // 页面加载完成后检查已有模块
        window.addEventListener('load', () => {
            log('🚀 页面加载完成，检查预加载模块...', 'info');
            checkModules();
        });
    </script>
</body>
</html>
