<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瞬光捕手 - Split-Second Spark</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/responsive.css">
</head>
<body>
    <!-- 游戏主容器 -->
    <div id="game-container">
        <!-- 加载界面 -->
        <div id="loading-screen" class="screen active">
            <div class="loading-content">
                <h1 class="game-title" data-i18n="game.title">瞬光捕手</h1>
                <div class="loading-spinner"></div>
                <p data-i18n="loading.text">正在加载...</p>
            </div>
        </div>

        <!-- 主菜单界面 -->
        <div id="main-menu" class="screen">
            <div class="menu-content">
                <h1 class="game-title" data-i18n="game.title">瞬光捕手</h1>
                <p class="game-subtitle" data-i18n="game.subtitle">捕捉决定性瞬间，引燃无限可能</p>
                
                <div class="menu-buttons">
                    <button id="start-game-btn" class="menu-btn primary" data-i18n="menu.start">开始游戏</button>
                    <button id="level-editor-btn" class="menu-btn" data-i18n="menu.levelEditor">关卡编辑器</button>
                    <button id="custom-levels-btn" class="menu-btn" data-i18n="menu.customLevels">自定义关卡</button>
                    <button id="leaderboard-btn" class="menu-btn" data-i18n="menu.leaderboard">排行榜</button>
                    <button id="settings-btn" class="menu-btn" data-i18n="menu.settings">设置</button>
                </div>

                <!-- 玩家信息 -->
                <div class="player-info">
                    <div class="current-player">
                        <span data-i18n="player.current">当前玩家:</span>
                        <span id="current-player-name">游客</span>
                        <button id="switch-player-btn" class="small-btn" data-i18n="player.switch">切换</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 游戏界面 -->
        <div id="game-screen" class="screen">
            <div class="game-ui">
                <div class="game-header">
                    <div class="score-info">
                        <span data-i18n="game.score">得分:</span>
                        <span id="current-score">0</span>
                    </div>
                    <div class="level-info">
                        <span data-i18n="game.level">关卡:</span>
                        <span id="current-level">1</span>
                    </div>
                    <div class="lives-info">
                        <span data-i18n="game.lives">生命:</span>
                        <span id="current-lives">3</span>
                    </div>
                    <button id="pause-btn" class="small-btn" data-i18n="game.pause">暂停</button>
                </div>
                
                <!-- 游戏画布区域 -->
                <div id="game-canvas-container">
                    <canvas id="game-canvas"></canvas>
                    <!-- 移动端触摸控制区域 -->
                    <div id="touch-controls" class="touch-controls">
                        <div class="touch-area" id="touch-area"></div>
                    </div>
                </div>

                <!-- 游戏提示信息 -->
                <div id="game-hints" class="game-hints">
                    <p id="hint-text" data-i18n="game.hint.ready">准备好了吗？</p>
                </div>
            </div>
        </div>

        <!-- 暂停菜单 -->
        <div id="pause-menu" class="overlay hidden">
            <div class="overlay-content">
                <h2 data-i18n="pause.title">游戏暂停</h2>
                <div class="menu-buttons">
                    <button id="resume-btn" class="menu-btn primary" data-i18n="pause.resume">继续游戏</button>
                    <button id="restart-btn" class="menu-btn" data-i18n="pause.restart">重新开始</button>
                    <button id="main-menu-btn" class="menu-btn" data-i18n="pause.mainMenu">主菜单</button>
                </div>
            </div>
        </div>

        <!-- 游戏结束界面 -->
        <div id="game-over" class="overlay hidden">
            <div class="overlay-content">
                <h2 data-i18n="gameOver.title">游戏结束</h2>
                <div class="final-score">
                    <span data-i18n="gameOver.finalScore">最终得分:</span>
                    <span id="final-score-value">0</span>
                </div>
                <div class="menu-buttons">
                    <button id="play-again-btn" class="menu-btn primary" data-i18n="gameOver.playAgain">再玩一次</button>
                    <button id="back-to-menu-btn" class="menu-btn" data-i18n="gameOver.backToMenu">返回主菜单</button>
                </div>
            </div>
        </div>

        <!-- 排行榜界面 -->
        <div id="leaderboard-screen" class="screen">
            <div class="leaderboard-content">
                <h2 data-i18n="leaderboard.title">排行榜</h2>

                <!-- 排行榜类型选择 -->
                <div class="leaderboard-tabs">
                    <button class="tab-btn active" data-type="global_high_score" data-i18n="leaderboard.global">全球排行</button>
                    <button class="tab-btn" data-type="daily_high_score" data-i18n="leaderboard.daily">今日排行</button>
                    <button class="tab-btn" data-type="weekly_high_score" data-i18n="leaderboard.weekly">本周排行</button>
                    <button class="tab-btn" data-type="perfect_hits" data-i18n="leaderboard.perfectHits">完美击中</button>
                </div>

                <!-- 排行榜列表 -->
                <div class="leaderboard-list">
                    <div class="leaderboard-header">
                        <span data-i18n="leaderboard.rank">排名</span>
                        <span data-i18n="leaderboard.player">玩家</span>
                        <span data-i18n="leaderboard.score">分数</span>
                        <span data-i18n="leaderboard.time">时间</span>
                    </div>
                    <div id="leaderboard-entries" class="leaderboard-entries">
                        <!-- 排行榜条目将在这里动态生成 -->
                    </div>
                </div>

                <!-- 玩家排名信息 -->
                <div class="player-rank-info">
                    <div id="player-rank-display" class="player-rank">
                        <!-- 玩家排名信息将在这里显示 -->
                    </div>
                </div>

                <div class="menu-buttons">
                    <button id="refresh-leaderboard-btn" class="menu-btn" data-i18n="leaderboard.refresh">刷新</button>
                    <button id="back-from-leaderboard-btn" class="menu-btn" data-i18n="common.back">返回</button>
                </div>
            </div>
        </div>

        <!-- 自定义关卡界面 -->
        <div id="custom-levels-screen" class="screen">
            <div class="custom-levels-layout">
                <!-- 顶部导航栏 -->
                <div class="custom-levels-header">
                    <div class="header-left">
                        <h2 data-i18n="customLevels.title">自定义关卡</h2>
                        <p data-i18n="customLevels.subtitle">探索玩家创造的精彩关卡</p>
                    </div>
                    <div class="header-right">
                        <button id="back-from-custom-levels-btn" class="menu-btn" data-i18n="common.back">返回</button>
                    </div>
                </div>

                <!-- 过滤和排序控制 -->
                <div class="levels-controls">
                    <div class="controls-section">
                        <div class="filter-controls">
                            <label data-i18n="customLevels.filter">过滤:</label>
                            <select id="level-filter">
                                <option value="published" data-i18n="customLevels.filter.published">已发布</option>
                                <option value="my" data-i18n="customLevels.filter.my">我的关卡</option>
                                <option value="all" data-i18n="customLevels.filter.all">全部</option>
                            </select>
                        </div>
                        <div class="sort-controls">
                            <label data-i18n="customLevels.sort">排序:</label>
                            <select id="level-sort">
                                <option value="rating" data-i18n="customLevels.sort.rating">评分</option>
                                <option value="playCount" data-i18n="customLevels.sort.playCount">游玩次数</option>
                                <option value="date" data-i18n="customLevels.sort.date">创建时间</option>
                                <option value="difficulty" data-i18n="customLevels.sort.difficulty">难度</option>
                            </select>
                        </div>
                        <div class="search-controls">
                            <input type="text" id="level-search" placeholder="搜索关卡..." data-i18n-placeholder="customLevels.search">
                            <button id="search-btn" class="icon-btn" title="搜索" data-i18n-title="customLevels.searchBtn">🔍</button>
                        </div>
                    </div>
                    <div class="stats-section">
                        <div class="level-stats">
                            <span data-i18n="customLevels.totalLevels">总关卡数:</span>
                            <span id="total-levels-count">0</span>
                        </div>
                    </div>
                </div>

                <!-- 关卡列表 -->
                <div class="levels-container">
                    <div id="levels-list" class="levels-grid">
                        <!-- 关卡卡片将在这里动态生成 -->
                    </div>

                    <!-- 加载状态 -->
                    <div id="levels-loading" class="loading-state hidden">
                        <div class="spinner"></div>
                        <p data-i18n="customLevels.loading">加载关卡中...</p>
                    </div>

                    <!-- 空状态 -->
                    <div id="levels-empty" class="empty-state hidden">
                        <div class="empty-icon">📦</div>
                        <h3 data-i18n="customLevels.empty.title">暂无关卡</h3>
                        <p data-i18n="customLevels.empty.message">还没有找到符合条件的关卡</p>
                        <button id="create-first-level-btn" class="menu-btn primary" data-i18n="customLevels.createFirst">创建第一个关卡</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 关卡详情对话框 -->
        <div id="level-detail-dialog" class="dialog hidden">
            <div class="dialog-content level-detail-content">
                <div class="level-detail-header">
                    <h3 id="level-detail-name">关卡名称</h3>
                    <button id="close-level-detail-btn" class="close-btn">×</button>
                </div>

                <div class="level-detail-body">
                    <div class="level-info-section">
                        <div class="level-meta">
                            <div class="meta-item">
                                <span class="meta-label" data-i18n="customLevels.author">作者:</span>
                                <span id="level-detail-author">作者名</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label" data-i18n="customLevels.difficulty">难度:</span>
                                <span id="level-detail-difficulty" class="difficulty-badge">普通</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label" data-i18n="customLevels.playCount">游玩次数:</span>
                                <span id="level-detail-play-count">0</span>
                            </div>
                            <div class="meta-item">
                                <span class="meta-label" data-i18n="customLevels.created">创建时间:</span>
                                <span id="level-detail-created">2024-01-01</span>
                            </div>
                        </div>

                        <div class="level-description">
                            <h4 data-i18n="customLevels.description">关卡描述</h4>
                            <p id="level-detail-description">关卡描述内容</p>
                        </div>

                        <div class="level-rating">
                            <div class="rating-display">
                                <div class="rating-item">
                                    <span class="rating-icon like">👍</span>
                                    <span id="level-detail-likes">0</span>
                                </div>
                                <div class="rating-item">
                                    <span class="rating-icon dislike">👎</span>
                                    <span id="level-detail-dislikes">0</span>
                                </div>
                                <div class="rating-score">
                                    <span data-i18n="customLevels.rating">评分:</span>
                                    <span id="level-detail-rating-score">0</span>
                                </div>
                            </div>

                            <div class="rating-actions">
                                <button id="like-level-btn" class="rating-btn like-btn" data-i18n="customLevels.like">点赞</button>
                                <button id="dislike-level-btn" class="rating-btn dislike-btn" data-i18n="customLevels.dislike">踩</button>
                            </div>
                        </div>
                    </div>

                    <!-- 关卡预览区域 -->
                    <div class="level-preview-section">
                        <h4 data-i18n="customLevels.preview">关卡预览</h4>
                        <div class="level-preview-container">
                            <canvas id="level-preview-canvas" width="300" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <div class="level-detail-actions">
                    <button id="play-level-btn" class="dialog-btn primary" data-i18n="customLevels.play">开始游戏</button>
                    <button id="edit-level-btn" class="dialog-btn" data-i18n="customLevels.edit">编辑关卡</button>
                    <button id="delete-level-btn" class="dialog-btn danger" data-i18n="customLevels.delete">删除关卡</button>
                </div>
            </div>
        </div>

        <!-- 关卡编辑器界面 -->
        <div id="level-editor-screen" class="screen">
            <div class="editor-layout">
                <!-- 工具栏 -->
                <div class="editor-toolbar">
                    <div class="toolbar-section">
                        <h3 data-i18n="editor.tools">工具</h3>
                        <div class="tool-buttons">
                            <button class="tool-btn active" data-tool="select" data-i18n="editor.select" title="选择工具">选择</button>
                            <button class="tool-btn" data-tool="spark" data-i18n="editor.spark" title="光点工具">光点</button>
                            <button class="tool-btn" data-tool="obstacle" data-i18n="editor.obstacle" title="障碍物工具">障碍</button>
                            <button class="tool-btn" data-tool="powerup" data-i18n="editor.powerup" title="道具工具">道具</button>
                            <button class="tool-btn" data-tool="trigger" data-i18n="editor.trigger" title="触发器工具">触发</button>
                            <button class="tool-btn" data-tool="eraser" data-i18n="editor.eraser" title="橡皮擦工具">橡皮</button>
                        </div>
                    </div>

                    <div class="toolbar-section">
                        <h3 data-i18n="editor.actions">操作</h3>
                        <div class="action-buttons">
                            <button id="new-level-btn" class="action-btn" data-i18n="editor.new">新建</button>
                            <button id="load-level-btn" class="action-btn" data-i18n="editor.load">加载</button>
                            <button id="save-level-btn" class="action-btn" data-i18n="editor.save">保存</button>
                            <button id="test-level-btn" class="action-btn" data-i18n="editor.test">测试</button>
                            <button id="publish-level-btn" class="action-btn" data-i18n="editor.publish">发布</button>
                        </div>
                    </div>

                    <div class="toolbar-section">
                        <h3 data-i18n="editor.view">视图</h3>
                        <div class="view-controls">
                            <label>
                                <input type="checkbox" id="show-grid" checked>
                                <span data-i18n="editor.showGrid">显示网格</span>
                            </label>
                            <label>
                                <input type="checkbox" id="snap-to-grid" checked>
                                <span data-i18n="editor.snapToGrid">对齐网格</span>
                            </label>
                            <div class="zoom-controls">
                                <button id="zoom-out-btn">-</button>
                                <span id="zoom-level">100%</span>
                                <button id="zoom-in-btn">+</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主编辑区域 -->
                <div class="editor-main">
                    <div class="editor-canvas-container">
                        <canvas id="level-editor-canvas"></canvas>
                    </div>
                </div>

                <!-- 属性面板 -->
                <div class="editor-properties">
                    <div class="properties-section">
                        <h3 data-i18n="editor.levelSettings">关卡设置</h3>
                        <div id="level-settings">
                            <div class="setting-item">
                                <label data-i18n="editor.levelName">关卡名称:</label>
                                <input type="text" id="level-name" placeholder="输入关卡名称">
                            </div>
                            <div class="setting-item">
                                <label data-i18n="editor.levelDescription">关卡描述:</label>
                                <textarea id="level-description" placeholder="输入关卡描述"></textarea>
                            </div>
                            <div class="setting-item">
                                <label data-i18n="editor.difficulty">难度:</label>
                                <select id="level-difficulty">
                                    <option value="easy" data-i18n="editor.difficulty.easy">简单</option>
                                    <option value="normal" data-i18n="editor.difficulty.normal">普通</option>
                                    <option value="hard" data-i18n="editor.difficulty.hard">困难</option>
                                    <option value="expert" data-i18n="editor.difficulty.expert">专家</option>
                                </select>
                            </div>
                            <div class="setting-item">
                                <label data-i18n="editor.timeLimit">时间限制(秒):</label>
                                <input type="number" id="time-limit" min="10" max="300" value="60">
                            </div>
                            <div class="setting-item">
                                <label data-i18n="editor.targetScore">目标分数:</label>
                                <input type="number" id="target-score" min="100" max="10000" value="1000">
                            </div>
                        </div>
                    </div>

                    <div class="properties-section">
                        <h3 data-i18n="editor.objectProperties">对象属性</h3>
                        <div id="property-panel">
                            <p data-i18n="editor.selectTool">请选择工具</p>
                        </div>
                    </div>

                    <div class="properties-section">
                        <h3 data-i18n="editor.statistics">统计信息</h3>
                        <div id="level-stats">
                            <div class="stat-item">
                                <span data-i18n="editor.totalObjects">总对象数:</span>
                                <span id="total-objects">0</span>
                            </div>
                            <div class="stat-item">
                                <span data-i18n="editor.sparkCount">光点数:</span>
                                <span id="spark-count">0</span>
                            </div>
                            <div class="stat-item">
                                <span data-i18n="editor.obstacleCount">障碍数:</span>
                                <span id="obstacle-count">0</span>
                            </div>
                            <div class="stat-item">
                                <span data-i18n="editor.powerupCount">道具数:</span>
                                <span id="powerup-count">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 关卡加载对话框 -->
            <div id="load-level-dialog" class="dialog hidden">
                <div class="dialog-content">
                    <h3 data-i18n="editor.loadLevel">加载关卡</h3>
                    <div class="level-list" id="level-list">
                        <!-- 关卡列表将在这里动态生成 -->
                    </div>
                    <div class="dialog-buttons">
                        <button id="load-selected-btn" class="dialog-btn primary" data-i18n="editor.loadSelected">加载选中</button>
                        <button id="cancel-load-btn" class="dialog-btn" data-i18n="common.cancel">取消</button>
                    </div>
                </div>
            </div>

            <!-- 返回按钮 -->
            <div class="editor-nav">
                <button id="back-from-editor-btn" class="nav-btn" data-i18n="common.back">返回主菜单</button>
            </div>
        </div>

        <!-- 设置界面 -->
        <div id="settings-screen" class="screen">
            <div class="settings-content">
                <h2 data-i18n="settings.title">设置</h2>
                
                <div class="setting-group">
                    <label data-i18n="settings.language">语言:</label>
                    <select id="language-select">
                        <option value="zh-CN">中文</option>
                        <option value="en-US">English</option>
                    </select>
                </div>

                <div class="setting-group">
                    <label data-i18n="settings.sound">音效:</label>
                    <input type="range" id="sound-volume" min="0" max="100" value="50">
                    <span id="sound-value">50%</span>
                </div>

                <div class="setting-group">
                    <label data-i18n="settings.music">音乐:</label>
                    <input type="range" id="music-volume" min="0" max="100" value="30">
                    <span id="music-value">30%</span>
                </div>

                <div class="menu-buttons">
                    <button id="save-settings-btn" class="menu-btn primary" data-i18n="settings.save">保存设置</button>
                    <button id="cancel-settings-btn" class="menu-btn" data-i18n="settings.cancel">取消</button>
                </div>
            </div>
        </div>

        <!-- 玩家管理界面 -->
        <div id="player-management" class="overlay hidden">
            <div class="overlay-content">
                <h2 data-i18n="player.management">玩家管理</h2>
                
                <div class="player-list">
                    <h3 data-i18n="player.existing">现有玩家:</h3>
                    <div id="existing-players"></div>
                </div>

                <div class="new-player">
                    <h3 data-i18n="player.createNew">创建新玩家:</h3>
                    <input type="text" id="new-player-name" placeholder="输入玩家名称" data-i18n-placeholder="player.namePlaceholder">
                    <button id="create-player-btn" class="small-btn" data-i18n="player.create">创建</button>
                </div>

                <div class="menu-buttons">
                    <button id="close-player-mgmt-btn" class="menu-btn" data-i18n="common.close">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript 模块 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/touch-helper.js"></script>
    <script src="js/core/game-engine.js"></script>
    <script src="js/core/player-manager.js"></script>
    <script src="js/core/level-manager.js"></script>
    <script src="js/core/leaderboard-manager.js"></script>
    <script src="js/core/level-editor.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
