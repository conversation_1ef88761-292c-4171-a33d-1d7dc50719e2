<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瞬光捕手 - 现代化效果测试</title>
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/modern-enhancements.css">
    <link rel="stylesheet" href="styles/responsive.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: var(--primary-bg);
            color: var(--text-primary);
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 15px;
            backdrop-filter: var(--glass-blur);
        }
        
        .test-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: var(--neon-blue);
        }
        
        .button-test {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        .effect-demo {
            width: 300px;
            height: 200px;
            background: #000;
            border: 2px solid var(--glass-border);
            border-radius: 10px;
            position: relative;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .spark-demo {
            position: absolute;
            width: 20px;
            height: 20px;
            background: radial-gradient(circle, var(--neon-blue), transparent);
            border-radius: 50%;
            animation: sparkPulse 2s ease-in-out infinite;
        }
        
        @keyframes sparkPulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.5); opacity: 1; }
        }
    </style>
</head>
<body>
    <!-- 动态粒子背景系统 -->
    <div class="particle-system" id="particle-system"></div>
    
    <div class="container">
        <h1 class="game-title" data-text="现代化效果测试">现代化效果测试</h1>
        <p class="game-subtitle">测试瞬光捕手的现代化视觉效果</p>
        
        <!-- 按钮效果测试 -->
        <div class="test-section">
            <h2 class="test-title">🎨 按钮效果测试</h2>
            <div class="button-test">
                <button class="menu-btn">普通按钮</button>
                <button class="menu-btn primary">主要按钮</button>
                <button class="menu-btn pulse-effect">脉冲按钮</button>
            </div>
        </div>
        
        <!-- 标题效果测试 -->
        <div class="test-section">
            <h2 class="test-title">✨ 标题效果测试</h2>
            <h1 class="game-title" data-text="瞬光捕手">瞬光捕手</h1>
            <p class="game-subtitle">捕捉决定性瞬间，引燃无限可能</p>
        </div>
        
        <!-- 光点效果演示 -->
        <div class="test-section">
            <h2 class="test-title">💫 光点效果演示</h2>
            <div class="effect-demo" id="spark-demo">
                <div class="spark-demo" style="top: 50px; left: 50px;"></div>
                <div class="spark-demo" style="top: 100px; left: 150px; animation-delay: 0.5s;"></div>
                <div class="spark-demo" style="top: 80px; left: 220px; animation-delay: 1s;"></div>
            </div>
        </div>
        
        <!-- 玻璃态效果测试 -->
        <div class="test-section">
            <h2 class="test-title">🔮 玻璃态效果测试</h2>
            <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                <div style="width: 200px; height: 100px; background: var(--glass-bg); border: 1px solid var(--glass-border); border-radius: 15px; backdrop-filter: var(--glass-blur); display: flex; align-items: center; justify-content: center;">
                    玻璃态卡片
                </div>
                <div style="width: 200px; height: 100px; background: rgba(0, 212, 255, 0.1); border: 1px solid var(--neon-blue); border-radius: 15px; backdrop-filter: blur(20px); display: flex; align-items: center; justify-content: center;">
                    霓虹玻璃态
                </div>
            </div>
        </div>
        
        <!-- 动画效果测试 -->
        <div class="test-section">
            <h2 class="test-title">🎭 动画效果测试</h2>
            <button onclick="testClickEffect(event)" class="menu-btn">点击测试特效</button>
            <button onclick="testSparkHit()" class="menu-btn primary">测试光点击中</button>
            <button onclick="testComboEffect()" class="menu-btn">测试连击效果</button>
        </div>
        
        <!-- 响应式测试 -->
        <div class="test-section">
            <h2 class="test-title">📱 响应式测试</h2>
            <p>调整浏览器窗口大小来测试响应式效果</p>
            <div class="button-test">
                <button class="menu-btn">响应式按钮 1</button>
                <button class="menu-btn primary">响应式按钮 2</button>
            </div>
        </div>
    </div>
    
    <script src="js/ui/modern-effects.js"></script>
    <script>
        // 测试点击特效
        function testClickEffect(event) {
            if (window.modernEffects) {
                window.modernEffects.createClickEffect(event.target, event.clientX, event.clientY);
            }
        }
        
        // 测试光点击中特效
        function testSparkHit() {
            if (window.modernEffects) {
                const demo = document.getElementById('spark-demo');
                const rect = demo.getBoundingClientRect();
                const x = rect.width / 2;
                const y = rect.height / 2;
                window.modernEffects.createSparkHitEffect(x, y, '#00d4ff');
            }
        }
        
        // 测试连击特效
        function testComboEffect() {
            if (window.modernEffects) {
                const demo = document.getElementById('spark-demo');
                const rect = demo.getBoundingClientRect();
                const x = rect.width / 2;
                const y = rect.height / 2;
                const combo = Math.floor(Math.random() * 10) + 3;
                window.modernEffects.createComboEffect(combo, x, y);
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎨 现代化效果测试页面加载完成');
            
            // 添加一些动态效果
            setTimeout(() => {
                const pulseBtn = document.querySelector('.pulse-effect');
                if (pulseBtn && window.modernEffects) {
                    window.modernEffects.addPulseEffect(pulseBtn);
                }
            }, 1000);
        });
    </script>
</body>
</html>
