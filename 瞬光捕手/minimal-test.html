<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最小测试</title>
</head>
<body>
    <h1>最小测试</h1>
    <div id="output"></div>

    <script>
        const output = document.getElementById('output');
        
        function log(message) {
            const p = document.createElement('p');
            p.textContent = message;
            output.appendChild(p);
            console.log(message);
        }
        
        // 全局错误捕获
        window.addEventListener('error', (event) => {
            log(`❌ 错误: ${event.message} (${event.filename}:${event.lineno})`);
        });
        
        log('开始测试...');
        
        // 测试1: 直接加载 screen-manager.js
        log('加载 screen-manager.js...');
        const script1 = document.createElement('script');
        script1.src = 'js/ui/screen-manager.js';
        
        script1.onload = () => {
            log('screen-manager.js 加载完成');
            
            setTimeout(() => {
                if (window.screenManager) {
                    log('✅ screenManager 已创建');
                } else {
                    log('❌ screenManager 未创建');
                }
                
                // 测试2: 加载 input-handler.js
                log('加载 input-handler.js...');
                const script2 = document.createElement('script');
                script2.src = 'js/ui/input-handler.js';
                
                script2.onload = () => {
                    log('input-handler.js 加载完成');
                    
                    setTimeout(() => {
                        if (window.inputHandler) {
                            log('✅ inputHandler 已创建');
                        } else {
                            log('❌ inputHandler 未创建');
                        }
                    }, 100);
                };
                
                script2.onerror = () => {
                    log('❌ input-handler.js 加载失败');
                };
                
                document.head.appendChild(script2);
            }, 100);
        };
        
        script1.onerror = () => {
            log('❌ screen-manager.js 加载失败');
        };
        
        document.head.appendChild(script1);
    </script>
</body>
</html>
